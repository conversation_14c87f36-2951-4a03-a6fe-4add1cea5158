import { convertPCM16ToFloat32 } from './utils';

export interface AudioOutputConfig {
  sampleRate?: number;
  channelCount?: number;
  bufferSize?: number;
  onError?: (error: Error) => void;
  onPlaybackStart?: () => void;
  onPlaybackEnd?: () => void;
}

export interface AudioOutputStatus {
  isPlaying: boolean;
  isMuted: boolean;
  playbackRate: number;
  volume: number;
  queuedDuration: number;
}

export interface AudioChunk {
  data: ArrayBuffer;
  timestamp: number;
  id: string;
}

export class AudioOutputManager {
  private audioContext: AudioContext | null = null;
  private gainNode: GainNode | null = null;
  private config: AudioOutputConfig;
  private status: AudioOutputStatus = {
    isPlaying: false,
    isMuted: false,
    playbackRate: 1.0,
    volume: 1.0,
    queuedDuration: 0
  };
  
  // Audio queue management
  private audioQueue: AudioChunk[] = [];
  private currentSource: AudioBufferSourceNode | null = null;
  private nextPlayTime = 0;
  private isProcessingQueue = false;
  
  // Playback rate control
  private playbackRateNode: AudioBufferSourceNode | null = null;
  private rateChangeBuffer: AudioBuffer[] = [];

  constructor(config: AudioOutputConfig = {}) {
    this.config = {
      sampleRate: 24000,
      channelCount: 1,
      bufferSize: 4096,
      ...config
    };
  }

  async initialize(): Promise<void> {
    try {
      // Create audio context optimized for low-latency playback
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)({
        sampleRate: this.config.sampleRate,
        latencyHint: 'interactive'
      });

      // Create gain node for volume and mute control
      this.gainNode = this.audioContext.createGain();
      this.gainNode.connect(this.audioContext.destination);
      this.gainNode.gain.value = this.status.volume;

      console.log('Audio output initialized:', {
        sampleRate: this.audioContext.sampleRate,
        latency: this.audioContext.baseLatency
      });

    } catch (error) {
      const errorMsg = `Failed to initialize audio output: ${error}`;
      console.error(errorMsg);
      this.config.onError?.(new Error(errorMsg));
      throw error;
    }
  }

  // Add audio data to the playback queue (streaming from WebSocket)
  addAudioData(audioData: ArrayBuffer): void {
    if (!this.audioContext) {
      console.warn('Audio context not initialized');
      return;
    }

    const chunk: AudioChunk = {
      data: audioData,
      timestamp: Date.now(),
      id: `chunk-${Date.now()}-${Math.random()}`
    };

    this.audioQueue.push(chunk);
    this.updateQueuedDuration();

    // Start processing if not already running
    if (!this.isProcessingQueue) {
      this.processAudioQueue();
    }
  }

  private async processAudioQueue(): Promise<void> {
    if (this.isProcessingQueue || !this.audioContext || !this.gainNode) {
      return;
    }

    this.isProcessingQueue = true;

    try {
      while (this.audioQueue.length > 0) {
        const chunk = this.audioQueue.shift()!;
        await this.playAudioChunk(chunk);
      }
    } catch (error) {
      console.error('Error processing audio queue:', error);
      this.config.onError?.(error as Error);
    } finally {
      this.isProcessingQueue = false;
      this.status.isPlaying = false;
      this.config.onPlaybackEnd?.();
    }
  }

  private async playAudioChunk(chunk: AudioChunk): Promise<void> {
    if (!this.audioContext || !this.gainNode) return;

    try {
      // Convert PCM16 to Float32 for Web Audio API
      const float32Data = convertPCM16ToFloat32(chunk.data);
      
      // Create audio buffer
      const audioBuffer = this.audioContext.createBuffer(
        this.config.channelCount || 1,
        float32Data.length,
        this.config.sampleRate || 24000
      );

      // Copy data to audio buffer
      audioBuffer.copyToChannel(float32Data, 0);

      // Create source node with playback rate control
      const sourceNode = this.audioContext.createBufferSource();
      sourceNode.buffer = audioBuffer;
      sourceNode.playbackRate.value = this.status.playbackRate;

      // Connect to gain node (for volume/mute control)
      sourceNode.connect(this.gainNode);

      // Calculate when to start playing (for seamless playback)
      const currentTime = this.audioContext.currentTime;
      const startTime = Math.max(currentTime, this.nextPlayTime);
      
      // Update next play time for seamless concatenation
      const duration = audioBuffer.duration / this.status.playbackRate;
      this.nextPlayTime = startTime + duration;

      // Set up completion handler
      return new Promise<void>((resolve) => {
        sourceNode.onended = () => {
          this.updateQueuedDuration();
          resolve();
        };

        // Start playback
        sourceNode.start(startTime);
        this.currentSource = sourceNode;
        
        if (!this.status.isPlaying) {
          this.status.isPlaying = true;
          this.config.onPlaybackStart?.();
        }
      });

    } catch (error) {
      console.error('Error playing audio chunk:', error);
      throw error;
    }
  }

  // Playback rate control (0.5x to 2x speed)
  setPlaybackRate(rate: number): void {
    // Clamp rate between 0.5 and 2.0
    const clampedRate = Math.max(0.5, Math.min(2.0, rate));
    this.status.playbackRate = clampedRate;

    // Update current playing source if exists
    if (this.currentSource) {
      this.currentSource.playbackRate.value = clampedRate;
    }

    console.log('Playback rate set to:', clampedRate);
  }

  getPlaybackRate(): number {
    return this.status.playbackRate;
  }

  // Volume control
  setVolume(volume: number): void {
    // Clamp volume between 0 and 1
    const clampedVolume = Math.max(0, Math.min(1, volume));
    this.status.volume = clampedVolume;

    if (this.gainNode) {
      // Smooth volume changes to prevent clicks
      this.gainNode.gain.setTargetAtTime(
        clampedVolume, 
        this.audioContext!.currentTime, 
        0.01
      );
    }
  }

  getVolume(): number {
    return this.status.volume;
  }

  // Mute/unmute control
  setMuted(muted: boolean): void {
    this.status.isMuted = muted;
    
    if (this.gainNode) {
      const targetGain = muted ? 0 : this.status.volume;
      this.gainNode.gain.setTargetAtTime(
        targetGain,
        this.audioContext!.currentTime,
        0.01
      );
    }
  }

  isMuted(): boolean {
    return this.status.isMuted;
  }

  toggleMute(): boolean {
    this.setMuted(!this.status.isMuted);
    return this.status.isMuted;
  }

  // Stop current playback and clear queue
  stop(): void {
    // Stop current source
    if (this.currentSource) {
      try {
        this.currentSource.stop();
      } catch (error) {
        // Ignore errors from stopping already stopped sources
      }
      this.currentSource = null;
    }

    // Clear queue
    this.audioQueue = [];
    this.nextPlayTime = 0;
    this.isProcessingQueue = false;
    this.status.isPlaying = false;
    this.updateQueuedDuration();

    console.log('Audio playback stopped and queue cleared');
  }

  // Pause/resume playback
  pause(): void {
    if (this.audioContext && this.audioContext.state === 'running') {
      this.audioContext.suspend();
    }
  }

  resume(): void {
    if (this.audioContext && this.audioContext.state === 'suspended') {
      this.audioContext.resume();
    }
  }

  // Get current status
  getStatus(): AudioOutputStatus {
    return { ...this.status };
  }

  // Calculate total duration of queued audio
  private updateQueuedDuration(): void {
    if (!this.audioContext) {
      this.status.queuedDuration = 0;
      return;
    }

    let totalSamples = 0;
    for (const chunk of this.audioQueue) {
      // Each sample is 2 bytes (16-bit PCM)
      totalSamples += chunk.data.byteLength / 2;
    }

    // Convert samples to duration in seconds
    this.status.queuedDuration = totalSamples / (this.config.sampleRate || 24000);
  }

  // Get audio output devices (for device switching)
  static async getAvailableDevices(): Promise<MediaDeviceInfo[]> {
    try {
      const devices = await navigator.mediaDevices.enumerateDevices();
      return devices.filter(device => device.kind === 'audiooutput');
    } catch (error) {
      console.error('Failed to enumerate audio output devices:', error);
      return [];
    }
  }

  // Switch audio output device (if supported)
  async switchDevice(deviceId: string): Promise<void> {
    if (!this.audioContext) {
      throw new Error('Audio context not initialized');
    }

    try {
      // Note: setSinkId is not widely supported yet
      if ('setSinkId' in this.audioContext) {
        await (this.audioContext as any).setSinkId(deviceId);
        console.log('Switched to audio device:', deviceId);
      } else {
        console.warn('Device switching not supported in this browser');
      }
    } catch (error) {
      console.error('Failed to switch audio device:', error);
      throw error;
    }
  }

  destroy(): void {
    this.stop();

    if (this.gainNode) {
      this.gainNode.disconnect();
      this.gainNode = null;
    }

    if (this.audioContext && this.audioContext.state !== 'closed') {
      this.audioContext.close();
      this.audioContext = null;
    }

    this.audioQueue = [];
    this.rateChangeBuffer = [];
    
    this.status = {
      isPlaying: false,
      isMuted: false,
      playbackRate: 1.0,
      volume: 1.0,
      queuedDuration: 0
    };
  }
}
