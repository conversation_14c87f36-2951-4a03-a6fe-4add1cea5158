import { 
  base64To<PERSON><PERSON><PERSON><PERSON><PERSON>er, 
  arrayBufferToBase64, 
  convertFloat32To<PERSON><PERSON>16, 
  convertPCM16ToFloat32,
  createAudioContext,
  formatSessionTime,
  isSessionNearTimeout
} from './utils';

export interface RealtimeConfig {
  ephemeralKey: string;
  systemPrompt?: string;
  voice?: string;
  onTranscript?: (text: string, isUser: boolean) => void;
  onAudioDelta?: (audioData: ArrayBuffer) => void;
  onSessionUpdate?: (status: SessionStatus) => void;
  onError?: (error: Error) => void;
}

export interface SessionStatus {
  connected: boolean;
  sessionId?: string;
  startTime?: number;
  remainingTime?: number;
  isNearTimeout?: boolean;
}

export interface AudioBuffer {
  data: ArrayBuffer[];
  totalLength: number;
}

export class RealtimeWebSocketManager {
  private ws: WebSocket | null = null;
  private config: RealtimeConfig;
  private audioContext: AudioContext | null = null;
  private microphoneStream: MediaStream | null = null;
  private audioWorkletNode: AudioWorkletNode | null = null;
  private sessionStatus: SessionStatus = { connected: false };
  private audioBuffer: AudioBuffer = { data: [], totalLength: 0 };
  private isRecording = false;
  private sessionStartTime: number | null = null;
  private sessionTimeoutId: NodeJS.Timeout | null = null;

  constructor(config: RealtimeConfig) {
    this.config = config;
  }

  async connect(): Promise<void> {
    try {
      // Initialize audio context
      this.audioContext = createAudioContext();
      
      // Setup microphone
      await this.setupMicrophone();
      
      // Connect WebSocket
      await this.connectWebSocket();
      
      // Start session timer
      this.startSessionTimer();
      
    } catch (error) {
      this.config.onError?.(error as Error);
      throw error;
    }
  }

  private async connectWebSocket(): Promise<void> {
    return new Promise((resolve, reject) => {
      const url = `wss://api.openai.com/v1/realtime?model=gpt-realtime-2025-08-28`;
      
      this.ws = new WebSocket(url);
      
      this.ws.onopen = () => {
        console.log('Connected to OpenAI Realtime API');
        this.sessionStartTime = Date.now();
        this.updateSessionStatus({ connected: true, startTime: this.sessionStartTime });
        
        // Send session configuration
        this.sendSessionUpdate();
        resolve();
      };

      this.ws.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          this.handleWebSocketMessage(message);
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };

      this.ws.onclose = () => {
        console.log('WebSocket connection closed');
        this.updateSessionStatus({ connected: false });
        this.cleanup();
      };

      this.ws.onerror = (error) => {
        console.error('WebSocket error:', error);
        this.config.onError?.(new Error('WebSocket connection failed'));
        reject(error);
      };
    });
  }

  private sendSessionUpdate(): void {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) return;

    const sessionConfig = {
      type: 'session.update',
      session: {
        modalities: ['text', 'audio'],
        voice: this.config.voice || 'alloy',
        instructions: this.config.systemPrompt || 'You are a helpful assistant.',
        turn_detection: null, // Disable server VAD - manual control only
        input_audio_transcription: { 
          model: 'whisper-1' 
        },
        input_audio_format: 'pcm16',
        output_audio_format: 'pcm16',
        tools: []
      }
    };

    this.ws.send(JSON.stringify(sessionConfig));
  }

  private async setupMicrophone(): Promise<void> {
    try {
      this.microphoneStream = await navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: 24000,
          channelCount: 1,
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        }
      });

      if (!this.audioContext) {
        throw new Error('Audio context not initialized');
      }

      // Create audio worklet for processing
      const source = this.audioContext.createMediaStreamSource(this.microphoneStream);
      
      // For now, use ScriptProcessorNode (will upgrade to AudioWorklet later)
      const processor = this.audioContext.createScriptProcessor(4096, 1, 1);
      
      processor.onaudioprocess = (event) => {
        if (!this.isRecording) return;
        
        const inputBuffer = event.inputBuffer;
        const inputData = inputBuffer.getChannelData(0);
        
        // Convert to PCM16 and send to server buffer
        const pcm16Buffer = convertFloat32ToPCM16(inputData);
        this.appendToServerBuffer(pcm16Buffer);
      };

      source.connect(processor);
      processor.connect(this.audioContext.destination);
      
      this.audioWorkletNode = processor as any; // Type compatibility
      
    } catch (error) {
      throw new Error(`Failed to setup microphone: ${error}`);
    }
  }

  private appendToServerBuffer(audioData: ArrayBuffer): void {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) return;

    const base64Audio = arrayBufferToBase64(audioData);
    
    const message = {
      type: 'input_audio_buffer.append',
      audio: base64Audio
    };

    this.ws.send(JSON.stringify(message));
  }

  // Manual trigger - key method for interview control
  public commitAudioAndRespond(): void {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) return;

    // Commit the buffered audio
    this.ws.send(JSON.stringify({ type: 'input_audio_buffer.commit' }));
    
    // Request response generation
    this.ws.send(JSON.stringify({ type: 'response.create' }));
  }

  public clearAudioBuffer(): void {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) return;
    
    this.ws.send(JSON.stringify({ type: 'input_audio_buffer.clear' }));
  }

  public startRecording(): void {
    this.isRecording = true;
  }

  public stopRecording(): void {
    this.isRecording = false;
  }

  private handleWebSocketMessage(message: any): void {
    switch (message.type) {
      case 'session.created':
        console.log('Session created:', message.session);
        this.updateSessionStatus({ 
          sessionId: message.session.id,
          connected: true 
        });
        break;

      case 'input_audio_buffer.speech_started':
        console.log('Speech started');
        break;

      case 'input_audio_buffer.speech_stopped':
        console.log('Speech stopped');
        break;

      case 'conversation.item.input_audio_transcription.completed':
        const userTranscript = message.transcript;
        console.log('User transcript:', userTranscript);
        this.config.onTranscript?.(userTranscript, true);
        break;

      case 'response.audio.delta':
        const audioData = base64ToArrayBuffer(message.delta);
        this.config.onAudioDelta?.(audioData);
        break;

      case 'response.audio_transcript.delta':
        const assistantTranscript = message.delta;
        console.log('Assistant transcript delta:', assistantTranscript);
        this.config.onTranscript?.(assistantTranscript, false);
        break;

      case 'response.done':
        console.log('Response completed');
        break;

      case 'error':
        console.error('Realtime API error:', message.error);
        this.config.onError?.(new Error(message.error.message));
        break;

      default:
        console.log('Unhandled message type:', message.type);
    }
  }

  private startSessionTimer(): void {
    // 30-minute session limit
    this.sessionTimeoutId = setTimeout(() => {
      this.handleSessionTimeout();
    }, 30 * 60 * 1000);

    // 5-minute warning
    setTimeout(() => {
      this.updateSessionStatus({ isNearTimeout: true });
    }, 25 * 60 * 1000);
  }

  private handleSessionTimeout(): void {
    console.log('Session timeout reached');
    this.disconnect();
    // Auto-reconnect logic would go here
  }

  private updateSessionStatus(updates: Partial<SessionStatus>): void {
    this.sessionStatus = { ...this.sessionStatus, ...updates };
    
    if (this.sessionStartTime) {
      const elapsed = Date.now() - this.sessionStartTime;
      this.sessionStatus.remainingTime = Math.max(0, (30 * 60 * 1000) - elapsed);
    }
    
    this.config.onSessionUpdate?.(this.sessionStatus);
  }

  public getSessionStatus(): SessionStatus {
    return { ...this.sessionStatus };
  }

  public disconnect(): void {
    this.cleanup();
  }

  private cleanup(): void {
    if (this.sessionTimeoutId) {
      clearTimeout(this.sessionTimeoutId);
      this.sessionTimeoutId = null;
    }

    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }

    if (this.audioWorkletNode) {
      this.audioWorkletNode.disconnect();
      this.audioWorkletNode = null;
    }

    if (this.microphoneStream) {
      this.microphoneStream.getTracks().forEach(track => track.stop());
      this.microphoneStream = null;
    }

    if (this.audioContext) {
      this.audioContext.close();
      this.audioContext = null;
    }

    this.isRecording = false;
    this.sessionStartTime = null;
    this.updateSessionStatus({ connected: false });
  }
}

// Assistant-UI Runtime Adapter
import type { AssistantRuntime, ThreadMessage } from '@assistant-ui/react';

export class RealtimeAssistantRuntime implements AssistantRuntime {
  private wsManager: RealtimeWebSocketManager | null = null;
  private messages: ThreadMessage[] = [];
  private currentMessage: Partial<ThreadMessage> | null = null;
  private subscribers = new Set<() => void>();

  constructor(private ephemeralKey: string, private systemPrompt?: string) {}

  async initialize(): Promise<void> {
    this.wsManager = new RealtimeWebSocketManager({
      ephemeralKey: this.ephemeralKey,
      systemPrompt: this.systemPrompt,
      onTranscript: (text, isUser) => {
        this.handleTranscript(text, isUser);
      },
      onAudioDelta: (audioData) => {
        this.handleAudioDelta(audioData);
      },
      onError: (error) => {
        console.error('Realtime error:', error);
      }
    });

    await this.wsManager.connect();
    this.wsManager.startRecording();
  }

  private handleTranscript(text: string, isUser: boolean): void {
    if (isUser) {
      // Add user message
      const userMessage: ThreadMessage = {
        id: `user-${Date.now()}`,
        role: 'user',
        content: [{ type: 'text', text }],
        createdAt: new Date()
      };
      this.messages.push(userMessage);
    } else {
      // Update or create assistant message
      if (!this.currentMessage) {
        this.currentMessage = {
          id: `assistant-${Date.now()}`,
          role: 'assistant',
          content: [{ type: 'text', text: '' }],
          createdAt: new Date()
        };
      }

      // Append to existing content
      if (this.currentMessage.content?.[0]?.type === 'text') {
        this.currentMessage.content[0].text += text;
      }
    }

    this.notifySubscribers();
  }

  private handleAudioDelta(audioData: ArrayBuffer): void {
    // Audio playback will be handled by the audio output pipeline
    // For now, we just log that we received audio
    console.log('Received audio delta:', audioData.byteLength, 'bytes');
  }

  // Assistant-UI Runtime interface methods
  getMessages(): ThreadMessage[] {
    const allMessages = [...this.messages];
    if (this.currentMessage) {
      allMessages.push(this.currentMessage as ThreadMessage);
    }
    return allMessages;
  }

  subscribe(callback: () => void): () => void {
    this.subscribers.add(callback);
    return () => this.subscribers.delete(callback);
  }

  async append(message: ThreadMessage): Promise<void> {
    this.messages.push(message);
    this.notifySubscribers();
  }

  async startRun(): Promise<void> {
    // Trigger the realtime response
    this.wsManager?.commitAudioAndRespond();
  }

  async cancelRun(): Promise<void> {
    // Clear the audio buffer and reset
    this.wsManager?.clearAudioBuffer();
    this.currentMessage = null;
    this.notifySubscribers();
  }

  // Custom methods for our interview assistant
  public triggerResponse(): void {
    this.wsManager?.commitAudioAndRespond();
  }

  public clearBuffer(): void {
    this.wsManager?.clearAudioBuffer();
  }

  public getSessionStatus() {
    return this.wsManager?.getSessionStatus();
  }

  private notifySubscribers(): void {
    this.subscribers.forEach(callback => callback());
  }

  public destroy(): void {
    this.wsManager?.disconnect();
    this.subscribers.clear();
  }
}
