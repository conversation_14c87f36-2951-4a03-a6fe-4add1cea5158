import { convertFloat32ToPCM16, arrayBufferToBase64 } from './utils';

export interface AudioInputConfig {
  sampleRate?: number;
  channelCount?: number;
  echoCancellation?: boolean;
  noiseSuppression?: boolean;
  autoGainControl?: boolean;
  onAudioData?: (audioData: A<PERSON>yBuffer) => void;
  onError?: (error: Error) => void;
}

export interface AudioInputStatus {
  isRecording: boolean;
  isInitialized: boolean;
  deviceName?: string;
  sampleRate?: number;
}

export class AudioInputManager {
  private audioContext: AudioContext | null = null;
  private mediaStream: MediaStream | null = null;
  private sourceNode: MediaStreamAudioSourceNode | null = null;
  private processorNode: ScriptProcessorNode | null = null;
  private config: AudioInputConfig;
  private status: AudioInputStatus = {
    isRecording: false,
    isInitialized: false
  };
  private bufferSize = 4096; // Process in 4KB chunks for low latency

  constructor(config: AudioInputConfig = {}) {
    this.config = {
      sampleRate: 24000,
      channelCount: 1,
      echoCancellation: true,
      noiseSuppression: true,
      autoGainControl: true,
      ...config
    };
  }

  async initialize(): Promise<void> {
    try {
      // Create audio context with optimal settings for realtime
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)({
        sampleRate: this.config.sampleRate,
        latencyHint: 'interactive'
      });

      // Request microphone access with specific constraints
      const constraints: MediaStreamConstraints = {
        audio: {
          sampleRate: this.config.sampleRate,
          channelCount: this.config.channelCount,
          echoCancellation: this.config.echoCancellation,
          noiseSuppression: this.config.noiseSuppression,
          autoGainControl: this.config.autoGainControl,
          // Additional constraints for better quality
          latency: 0.01, // 10ms latency target
          volume: 1.0
        }
      };

      this.mediaStream = await navigator.mediaDevices.getUserMedia(constraints);
      
      // Get device information
      const audioTracks = this.mediaStream.getAudioTracks();
      if (audioTracks.length > 0) {
        this.status.deviceName = audioTracks[0].label;
        this.status.sampleRate = this.audioContext.sampleRate;
      }

      // Create audio processing pipeline
      this.setupAudioProcessing();
      
      this.status.isInitialized = true;
      console.log('Audio input initialized:', {
        sampleRate: this.audioContext.sampleRate,
        device: this.status.deviceName
      });

    } catch (error) {
      const errorMsg = `Failed to initialize audio input: ${error}`;
      console.error(errorMsg);
      this.config.onError?.(new Error(errorMsg));
      throw error;
    }
  }

  private setupAudioProcessing(): void {
    if (!this.audioContext || !this.mediaStream) {
      throw new Error('Audio context or media stream not available');
    }

    // Create source node from microphone stream
    this.sourceNode = this.audioContext.createMediaStreamSource(this.mediaStream);

    // Create script processor for real-time audio processing
    // Note: ScriptProcessorNode is deprecated but still widely supported
    // TODO: Migrate to AudioWorklet for better performance
    this.processorNode = this.audioContext.createScriptProcessor(
      this.bufferSize, 
      this.config.channelCount || 1, 
      this.config.channelCount || 1
    );

    // Set up audio processing callback
    this.processorNode.onaudioprocess = (event) => {
      if (!this.status.isRecording) return;

      const inputBuffer = event.inputBuffer;
      const inputData = inputBuffer.getChannelData(0); // Get mono channel

      // Convert Float32 to PCM16 for OpenAI Realtime API
      const pcm16Buffer = convertFloat32ToPCM16(inputData);
      
      // Send to callback (will be sent to WebSocket)
      this.config.onAudioData?.(pcm16Buffer);
    };

    // Connect the audio processing pipeline
    this.sourceNode.connect(this.processorNode);
    
    // Connect to destination to prevent garbage collection
    // (required for ScriptProcessorNode to work)
    this.processorNode.connect(this.audioContext.destination);
  }

  startRecording(): void {
    if (!this.status.isInitialized) {
      throw new Error('Audio input not initialized');
    }

    if (this.audioContext?.state === 'suspended') {
      // Resume audio context if suspended (common on mobile)
      this.audioContext.resume();
    }

    this.status.isRecording = true;
    console.log('Audio recording started');
  }

  stopRecording(): void {
    this.status.isRecording = false;
    console.log('Audio recording stopped');
  }

  getStatus(): AudioInputStatus {
    return { ...this.status };
  }

  // Get current audio levels for UI feedback
  getCurrentLevel(): number {
    // This would require additional processing to calculate RMS
    // For now, return 0 (can be enhanced later)
    return 0;
  }

  // Mute/unmute microphone
  setMuted(muted: boolean): void {
    if (!this.mediaStream) return;

    this.mediaStream.getAudioTracks().forEach(track => {
      track.enabled = !muted;
    });
  }

  isMuted(): boolean {
    if (!this.mediaStream) return true;
    
    const audioTracks = this.mediaStream.getAudioTracks();
    return audioTracks.length === 0 || !audioTracks[0].enabled;
  }

  // Get available audio input devices
  static async getAvailableDevices(): Promise<MediaDeviceInfo[]> {
    try {
      const devices = await navigator.mediaDevices.enumerateDevices();
      return devices.filter(device => device.kind === 'audioinput');
    } catch (error) {
      console.error('Failed to enumerate audio devices:', error);
      return [];
    }
  }

  // Switch to a different audio input device
  async switchDevice(deviceId: string): Promise<void> {
    if (this.status.isRecording) {
      this.stopRecording();
    }

    // Clean up current stream
    this.cleanup();

    // Update config with new device
    this.config = {
      ...this.config,
      // Add deviceId to constraints when reinitializing
    };

    // Reinitialize with new device
    const constraints: MediaStreamConstraints = {
      audio: {
        deviceId: { exact: deviceId },
        sampleRate: this.config.sampleRate,
        channelCount: this.config.channelCount,
        echoCancellation: this.config.echoCancellation,
        noiseSuppression: this.config.noiseSuppression,
        autoGainControl: this.config.autoGainControl
      }
    };

    this.mediaStream = await navigator.mediaDevices.getUserMedia(constraints);
    this.setupAudioProcessing();
  }

  private cleanup(): void {
    if (this.processorNode) {
      this.processorNode.disconnect();
      this.processorNode = null;
    }

    if (this.sourceNode) {
      this.sourceNode.disconnect();
      this.sourceNode = null;
    }

    if (this.mediaStream) {
      this.mediaStream.getTracks().forEach(track => track.stop());
      this.mediaStream = null;
    }
  }

  destroy(): void {
    this.stopRecording();
    this.cleanup();

    if (this.audioContext && this.audioContext.state !== 'closed') {
      this.audioContext.close();
      this.audioContext = null;
    }

    this.status = {
      isRecording: false,
      isInitialized: false
    };
  }
}

// Audio level analyzer for visual feedback
export class AudioLevelAnalyzer {
  private analyserNode: AnalyserNode | null = null;
  private dataArray: Uint8Array | null = null;
  private animationId: number | null = null;

  constructor(private audioContext: AudioContext, private sourceNode: AudioNode) {
    this.setupAnalyzer();
  }

  private setupAnalyzer(): void {
    this.analyserNode = this.audioContext.createAnalyser();
    this.analyserNode.fftSize = 256;
    this.analyserNode.smoothingTimeConstant = 0.8;
    
    this.dataArray = new Uint8Array(this.analyserNode.frequencyBinCount);
    this.sourceNode.connect(this.analyserNode);
  }

  startAnalysis(callback: (level: number) => void): void {
    if (!this.analyserNode || !this.dataArray) return;

    const analyze = () => {
      this.analyserNode!.getByteFrequencyData(this.dataArray!);
      
      // Calculate RMS level
      let sum = 0;
      for (let i = 0; i < this.dataArray!.length; i++) {
        sum += this.dataArray![i] * this.dataArray![i];
      }
      const rms = Math.sqrt(sum / this.dataArray!.length);
      const level = rms / 255; // Normalize to 0-1
      
      callback(level);
      this.animationId = requestAnimationFrame(analyze);
    };

    analyze();
  }

  stopAnalysis(): void {
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
      this.animationId = null;
    }
  }

  destroy(): void {
    this.stopAnalysis();
    if (this.analyserNode) {
      this.analyserNode.disconnect();
      this.analyserNode = null;
    }
  }
}
