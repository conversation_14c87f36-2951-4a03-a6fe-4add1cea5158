"use client";

import { useState, useEffect } from "react";
import { RealtimeAssistant } from "@/components/realtime-assistant";
import { Button } from "@/components/ui/button";

type EphemeralResp = { ephemeralKey: string };

export default function Page() {
  const [ephemeralKey, setEphemeralKey] = useState<string | null>(null);
  const [connecting, setConnecting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const connect = async () => {
    try {
      setConnecting(true);
      setError(null);

      const res = await fetch("/api/realtime/ephemeral", { method: "POST" });
      if (!res.ok) throw new Error(await res.text());

      const data: EphemeralResp = await res.json();
      const ek = data?.ephemeralKey;

      if (!ek || !ek.startsWith("ek_")) {
        throw new Error("Server did not return a valid ephemeral key");
      }

      setEphemeralKey(ek);
    } catch (err: any) {
      console.error(err);
      setError(err?.message || String(err));
    } finally {
      setConnecting(false);
    }
  };

  const disconnect = () => {
    setEphemeralKey(null);
  };

  // Auto-connect on page load
  useEffect(() => {
    connect();
  }, []);

  if (ephemeralKey) {
    return (
      <RealtimeAssistant
        ephemeralKey={ephemeralKey}
        systemPrompt={`You are an expert system design interviewer for senior technical roles.

Your role:
- Listen to conversations between the interviewee and human interviewer
- Only respond when explicitly triggered (when someone says "AI, your turn" or similar)
- Provide insightful questions, clarifications, or feedback about system design concepts
- Help guide the interview toward deeper technical discussions
- Maintain context across the entire conversation

Key behaviors:
- Wait for manual triggers - do not interrupt natural conversation flow
- Ask probing questions about scalability, reliability, and trade-offs
- Suggest alternative approaches or edge cases to consider
- Keep responses concise but technically deep
- Adapt difficulty based on the candidate's responses

Remember: This is a practice session to help build genuine interview skills.`}
      />
    );
  }

  return (
    <main className="flex items-center justify-center min-h-screen bg-gray-50">
      <div className="text-center max-w-md mx-auto p-6">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            Speech-to-Speech Interview Assistant
          </h1>
          <p className="text-gray-600">
            Practice system design interviews with AI-powered feedback
          </p>
        </div>

        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-700 text-sm">{error}</p>
          </div>
        )}

        <div className="space-y-4">
          <Button
            onClick={connect}
            disabled={connecting}
            className="w-full"
            size="lg"
          >
            {connecting ? "Connecting..." : "Start Interview Session"}
          </Button>

          <div className="text-xs text-gray-500 space-y-1">
            <p>• Microphone access required</p>
            <p>• Use headphones to prevent echo</p>
            <p>• Press spacebar to trigger AI responses</p>
          </div>
        </div>
      </div>
    </main>
  );
}
