@tailwind base;
@tailwind components;
@tailwind utilities;

/* Assistant UI CSS Variables */
:root {
  --thread-max-width: 768px;
  --thread-padding-x: 1rem;
}

/* Assistant UI Base Styles */
.aui-thread-viewport {
  @apply flex min-w-0 flex-1 flex-col gap-6 overflow-y-auto px-4 py-6;
}

.aui-composer-root {
  @apply relative flex w-full flex-col rounded-2xl border border-gray-200 bg-white focus-within:ring-2 focus-within:ring-blue-500 focus-within:ring-offset-2;
}

.aui-composer-input {
  @apply min-h-[60px] w-full resize-none border-0 bg-transparent px-4 py-3 text-sm placeholder-gray-500 focus:outline-none;
}

.aui-message-user {
  @apply ml-auto max-w-[80%] rounded-2xl bg-blue-500 px-4 py-2 text-white;
}

.aui-message-assistant {
  @apply mr-auto max-w-[80%] rounded-2xl bg-gray-100 px-4 py-2 text-gray-900;
}

/* Audio Controls */
.audio-controls {
  @apply fixed bottom-4 right-4 z-50 flex flex-col gap-2 rounded-lg bg-white p-4 shadow-lg border;
}

.audio-control-button {
  @apply rounded-lg px-4 py-2 text-sm font-medium transition-colors;
}

.audio-control-button.primary {
  @apply bg-blue-500 text-white hover:bg-blue-600 disabled:opacity-50;
}

.audio-control-button.secondary {
  @apply bg-gray-200 text-gray-700 hover:bg-gray-300 disabled:opacity-50;
}

.audio-control-button.danger {
  @apply bg-red-500 text-white hover:bg-red-600 disabled:opacity-50;
}

/* Status indicators */
.status-indicator {
  @apply inline-flex items-center gap-2 rounded-full px-3 py-1 text-sm;
}

.status-indicator.connected {
  @apply bg-green-100 text-green-800;
}

.status-indicator.disconnected {
  @apply bg-red-100 text-red-800;
}

.status-indicator.connecting {
  @apply bg-yellow-100 text-yellow-800;
}

/* Transcript display */
.transcript-container {
  @apply fixed bottom-20 left-4 max-w-md rounded-lg bg-white/90 p-3 shadow-lg backdrop-blur-sm;
}

.transcript-text {
  @apply text-sm text-gray-700;
}

.transcript-label {
  @apply text-xs font-medium text-gray-500 uppercase tracking-wide;
}
