{"name": "realtime-webrtc", "private": true, "version": "0.0.0", "engines": {"node": ">=20.19 <21 || >=22.12"}, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@assistant-ui/react": "^0.10.45", "@assistant-ui/react-ai-sdk": "^1.0.5", "@assistant-ui/react-markdown": "^0.10.9", "@openai/agents-realtime": "^0.1.0", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.8", "@tailwindcss/postcss": "^4.1.12", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.23.12", "lucide-react": "^0.542.0", "next": "^14.2.10", "react": "^18.3.1", "react-dom": "^18.3.1", "remark-gfm": "^4.0.1", "tailwind-merge": "^3.3.1", "tw-animate-css": "^1.3.7"}, "devDependencies": {"@tailwindcss/typography": "^0.5.16", "@types/react": "19.1.12", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^4.1.12", "typescript": "~5.8.3"}}