#!/usr/bin/env node

/**
 * Test script to verify chat history and system prompt functionality
 */

const fetch = require('node-fetch');

const BACKEND_URL = 'http://localhost:8080';

async function testChatHistory() {
  console.log('🧪 Testing Chat History and System Prompt functionality...\n');

  // Test 1: Simple message with system prompt
  console.log('📝 Test 1: Simple message with system prompt');
  try {
    const response1 = await fetch(`${BACKEND_URL}/api/chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        context: 'Hello, what can you help me with?',
        systemPrompt: 'You are an AI assistant for "The Instrument" - a powerful screen capture and analysis system.',
        messages: []
      })
    });

    if (response1.ok) {
      console.log('✅ Test 1 passed: Backend accepted request with system prompt');
    } else {
      console.log('❌ Test 1 failed:', response1.status, response1.statusText);
    }
  } catch (error) {
    console.log('❌ Test 1 error:', error.message);
  }

  // Test 2: Message with conversation history
  console.log('\n📝 Test 2: Message with conversation history');
  try {
    const mockHistory = [
      {
        id: 'msg-1',
        role: 'user',
        content: [{ type: 'text', text: 'Hello' }],
        createdAt: new Date()
      },
      {
        id: 'msg-2',
        role: 'assistant',
        content: [{ type: 'text', text: 'Hello! How can I help you with screen analysis today?' }],
        createdAt: new Date()
      }
    ];

    const response2 = await fetch(`${BACKEND_URL}/api/chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        context: 'Can you analyze this screen?',
        systemPrompt: 'You are an AI assistant for "The Instrument".',
        messages: mockHistory
      })
    });

    if (response2.ok) {
      console.log('✅ Test 2 passed: Backend accepted request with conversation history');
    } else {
      console.log('❌ Test 2 failed:', response2.status, response2.statusText);
    }
  } catch (error) {
    console.log('❌ Test 2 error:', error.message);
  }

  // Test 3: Check backend logs for proper handling
  console.log('\n📝 Test 3: Check if backend processes system prompt and history');
  console.log('   Check the backend terminal for log messages showing:');
  console.log('   - System prompt processing');
  console.log('   - Conversation history count');

  console.log('\n🎉 Chat history and system prompt tests completed!');
  console.log('   Check the backend logs to verify the features are working correctly.');
}

// Run the test
testChatHistory().catch(console.error);
