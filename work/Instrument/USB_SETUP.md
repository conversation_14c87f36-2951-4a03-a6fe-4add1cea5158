# USB Connection Setup for The Instrument

The Instrument system now uses USB connectivity for reliable communication between the iOS device and the backend server.

## Prerequisites

1. **iPhone connected via USB cable** to your MacBook
2. **libimobiledevice** installed via Homebrew
3. **iproxy** tool (included with libimobiledevice)

## Setup Instructions

### 1. Install libimobiledevice (if not already installed)

```bash
brew install libimobiledevice
```

### 2. Connect your iPhone via USB

- Connect your iPhone to your MacBook using a USB cable
- Trust the computer when prompted on your iPhone

### 3. Verify device connection

```bash
xcrun devicectl list devices
```

You should see your iPhone listed as "connected".

### 4. Start port forwarding

```bash
iproxy 8082 8082
```

This command forwards port 8082 from your iPhone to localhost:8082 on your MacBook.

**Keep this terminal running** - the connection will be lost if you stop iproxy.

### 5. Start the backend server

In a new terminal:

```bash
cd backend
npm run dev
```

### 6. Start the frontend

In another terminal:

```bash
cd frontend-assistant-ui
npm run dev
```

## Verification

1. Open your browser to `http://localhost:3000`
2. You should see "Device Connected" status
3. The capture functionality should work immediately

## Troubleshooting

### Device not connecting
- Ensure iproxy is running: `iproxy 8082 8082`
- Check if iPhone is detected: `xcrun devicectl list devices`
- Restart the backend server

### Connection lost
- Check if iproxy process is still running
- Restart iproxy if needed
- The backend will automatically reconnect

### Port conflicts
- If port 8082 is in use, kill existing processes:
  ```bash
  lsof -ti:8082 | xargs kill -9
  ```

## Benefits of USB Connection

- **Reliable**: No network connectivity issues
- **Fast**: Direct USB connection for low latency
- **Secure**: No network traffic, all communication over USB
- **Consistent**: Works regardless of WiFi network configuration

## Network Discovery Removed

The previous network-based discovery using Bonjour has been removed for simplicity and reliability. USB connection is now the only supported method.
