const { Bonjour } = require('bonjour-service');

console.log('🔍 Testing Bonjour service discovery...');

const bonjour = new Bonjour();

// Browse for all services
console.log('📡 Browsing for all services...');
const allBrowser = bonjour.find({}, (service) => {
  console.log('🔍 Found service:', {
    name: service.name,
    type: service.type,
    port: service.port,
    host: service.host,
    addresses: service.addresses,
    fqdn: service.fqdn
  });
});

// Specifically look for _instrument._tcp
console.log('📡 Looking specifically for _instrument._tcp...');
const instrumentBrowser = bonjour.find({ type: '_instrument._tcp' }, (service) => {
  console.log('🎯 Found _instrument._tcp service:', {
    name: service.name,
    type: service.type,
    port: service.port,
    host: service.host,
    addresses: service.addresses,
    fqdn: service.fqdn,
    referer: service.referer
  });
});

// Keep the script running for 30 seconds
setTimeout(() => {
  console.log('⏰ Test complete, shutting down...');
  bonjour.destroy();
  process.exit(0);
}, 30000);

console.log('⏳ Waiting for 30 seconds to discover services...');
