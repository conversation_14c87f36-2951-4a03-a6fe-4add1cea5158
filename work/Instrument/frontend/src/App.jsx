import { useState, useEffect } from 'react'
import { AssistantRuntimeProvider, useLocalRuntime } from "@assistant-ui/react"
import { InstrumentManager } from "./lib/instrument-runtime"
import { Thread } from "./components/assistant-ui/thread"
import { ThreadList } from "./components/assistant-ui/thread-list"
import { ImagePreview } from "./components/assistant-ui/image-preview"
import './styles/instrument-ui.css'

function App() {
  // State management
  const [connectionStatus, setConnectionStatus] = useState(false)
  const [deviceInfo, setDeviceInfo] = useState(null)
  const [images, setImages] = useState([])
  const [instrumentManager, setInstrumentManager] = useState(null)

  // Backend configuration
  const BACKEND_URL = 'http://localhost:3000'
  const WS_URL = 'ws://localhost:8081'

  // Initialize instrument manager
  useEffect(() => {
    const manager = new InstrumentManager({
      backendUrl: BACKEND_URL,
      wsUrl: WS_URL,
      onDeviceStatusChange: (connected, deviceInfo) => {
        setConnectionStatus(connected)
        setDeviceInfo(deviceInfo)
      },
      onImagesReceived: (images) => {
        setImages(images)
      }
    })

    setInstrumentManager(manager)

    // Cleanup on unmount
    return () => {
      manager.destroy()
    }
  }, [])

  // Create a default adapter that will be replaced when instrumentManager is ready
  const defaultAdapter = {
    async *run() {
      yield { content: [{ type: "text", text: "Initializing..." }] };
    }
  };

  // Create the runtime using useLocalRuntime
  const runtime = useLocalRuntime(
    instrumentManager ? instrumentManager.createAdapter() : defaultAdapter
  )

  // Handler functions for UI actions
  const handleCapture = () => {
    if (instrumentManager) {
      instrumentManager.captureImages(1)
    }
  }

  const handleSend = () => {
    if (instrumentManager) {
      instrumentManager.sendToDevice()
    }
  }

  if (!instrumentManager || !runtime) {
    return (
      <div className="instrument-app">
        <div className="status-bar">
          <span>Loading...</span>
        </div>
      </div>
    )
  }

  return (
    <AssistantRuntimeProvider runtime={runtime}>
      <div className="instrument-app">
        {/* Connection Status */}
        <div className="status-bar">
          <span className={`status ${connectionStatus ? 'connected' : 'disconnected'}`}>
            Device: {connectionStatus ? 'Connected' : 'Disconnected'}
          </span>
          {deviceInfo && (
            <span className="device-info">
              ({deviceInfo.name} - {deviceInfo.host}:{deviceInfo.port})
            </span>
          )}
        </div>

        {/* Main Layout */}
        <div className="instrument-layout">
          <ThreadList />
          <Thread
            onCapture={handleCapture}
            onSendToDevice={handleSend}
            deviceConnected={connectionStatus}
            imageCount={images.length}
          />
        </div>

        {/* Image Preview */}
        <ImagePreview
          images={images}
          className="absolute bottom-4 left-4 max-w-xs"
        />
      </div>
    </AssistantRuntimeProvider>
  )
}

export default App
