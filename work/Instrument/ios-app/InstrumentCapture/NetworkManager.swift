import Foundation
import Network
import UIKit
import CryptoKit

protocol NetworkManagerDelegate: AnyObject {
    func networkManager(_ manager: NetworkManager, didReceiveCommand command: NetworkCommand)
    func networkManager(_ manager: NetworkManager, didChangeConnectionStatus connected: Bool)
    func networkManager(_ manager: NetworkManager, didFailWithError error: Error)
}

struct NetworkCommand {
    let type: String
    let parameters: [String: Any]
    
    init?(from data: Data) {
        guard let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
              let type = json["type"] as? String else {
            return nil
        }
        
        self.type = type
        self.parameters = json
    }
    
    func toData() -> Data? {
        return try? JSONSerialization.data(withJSONObject: parameters)
    }
}

class NetworkManager: NSObject {
    
    // MARK: - Properties
    weak var delegate: NetworkManagerDelegate?
    
    private var bonjourService: NetService?
    private var webSocketServer: NWListener?
    private var connectedClients: [NWConnection] = []
    private var connectionStates: [String: Bool] = [:] // Track handshake completion by endpoint
    private let serviceType = "_instrument._tcp"
    private let serviceName = "InstrumentCapture"
    private let port: UInt16 = 8082

    private var isServicePublished = false
    private var isServerRunning = false
    
    // MARK: - Public Methods
    func startService() {
        startWebSocketServer()
        publishBonjourService()
    }
    
    func stopService() {
        stopBonjourService()
        stopWebSocketServer()
    }
    
    func sendResponse(_ response: [String: Any]) {
        print("🔍 NetworkManager.sendResponse called:")
        print("   - Response type: \(response["type"] ?? "unknown")")
        print("   - Connected clients: \(connectedClients.count)")

        guard let data = try? JSONSerialization.data(withJSONObject: response) else {
            print("❌ Failed to serialize response to JSON")
            return
        }

        print("   - JSON data size: \(data.count) bytes")

        // Create proper WebSocket frame for the JSON data
        let framedData = createWebSocketFrame(data: data)
        print("   - Framed data size: \(framedData.count) bytes")

        var sentCount = 0
        // Send to all connected clients
        for connection in connectedClients {
            if connection.state == .ready {
                let isHandshaked = connectionStates[connection.endpoint.debugDescription] ?? false
                print("   - Client \(connection.endpoint): ready=\(connection.state == .ready), handshaked=\(isHandshaked)")
                if isHandshaked {
                    connection.send(content: framedData, completion: .contentProcessed { error in
                        if let error = error {
                            print("❌ Failed to send message to \(connection.endpoint): \(error)")
                        } else {
                            print("✅ Successfully sent message to \(connection.endpoint)")
                        }
                    })
                    sentCount += 1
                }
            }
        }
        print("📤 Sent response to \(sentCount) clients")
    }
    
    func sendImages(_ images: [Data]) {
        print("🔍 NetworkManager.sendImages called:")
        print("   - Number of images: \(images.count)")
        print("   - Image sizes: \(images.map { $0.count })")

        let base64Images = images.map { $0.base64EncodedString() }
        print("   - Base64 string lengths: \(base64Images.map { $0.count })")

        let response: [String: Any] = [
            "type": "images",
            "images": base64Images,
            "timestamp": ISO8601DateFormatter().string(from: Date())
        ]

        print("📤 Sending images response to backend")
        sendResponse(response)
    }
    
    func sendAcknowledgment(for commandType: String) {
        let response: [String: Any] = [
            "type": "ack",
            "command": commandType,
            "timestamp": ISO8601DateFormatter().string(from: Date())
        ]
        sendResponse(response)
    }
    
    func sendError(_ error: Error) {
        let response: [String: Any] = [
            "type": "error",
            "message": error.localizedDescription,
            "timestamp": ISO8601DateFormatter().string(from: Date())
        ]
        sendResponse(response)
    }
    
    // MARK: - Private Methods - Bonjour Service
    private func publishBonjourService() {
        guard !isServicePublished else { return }
        
        bonjourService = NetService(domain: "", type: serviceType, name: serviceName, port: Int32(port))
        bonjourService?.delegate = self
        
        // Add TXT record with device info - convert strings to Data
        let txtData: [String: Data] = [
            "device": UIDevice.current.name.data(using: .utf8) ?? Data(),
            "model": UIDevice.current.model.data(using: .utf8) ?? Data(),
            "version": "1.0".data(using: .utf8) ?? Data()
        ]
        
        let txtRecord = NetService.data(fromTXTRecord: txtData)
        bonjourService?.setTXTRecord(txtRecord)
        
        bonjourService?.publish()
        print("📡 Publishing Bonjour service: \(serviceName).\(serviceType) on port \(port)")
    }
    
    private func stopBonjourService() {
        guard isServicePublished else { return }
        
        bonjourService?.stop()
        bonjourService = nil
        isServicePublished = false
        print("🛑 Stopped Bonjour service")
    }
    
    // MARK: - Private Methods - WebSocket Server
    private func startWebSocketServer() {
        guard !isServerRunning else { return }
        
        do {
            let parameters = NWParameters.tcp
            parameters.allowLocalEndpointReuse = true
            
            webSocketServer = try NWListener(using: parameters, on: NWEndpoint.Port(rawValue: port)!)
            
            webSocketServer?.newConnectionHandler = { [weak self] connection in
                self?.handleNewConnection(connection)
            }
            
            webSocketServer?.start(queue: .main)
            isServerRunning = true
            
            print("🚀 WebSocket server started on port \(port)")
            
        } catch {
            delegate?.networkManager(self, didFailWithError: error)
        }
    }
    
    private func stopWebSocketServer() {
        guard isServerRunning else { return }
        
        // Close all connections
        for connection in connectedClients {
            connection.cancel()
        }
        connectedClients.removeAll()
        connectionStates.removeAll()
        
        webSocketServer?.cancel()
        webSocketServer = nil
        isServerRunning = false
        
        delegate?.networkManager(self, didChangeConnectionStatus: false)
        print("🛑 Stopped WebSocket server")
    }
    
    private func handleNewConnection(_ connection: NWConnection) {
        NSLog("🔗 New connection from: \(connection.endpoint)")
        print("🔗 New connection from: \(connection.endpoint)")

        connection.start(queue: .main)
        connectedClients.append(connection)
        connectionStates[connection.endpoint.debugDescription] = false // Not yet handshaked

        // Set up message receiving for WebSocket handshake
        receiveMessage(on: connection)
        
        // Monitor connection state
        connection.stateUpdateHandler = { [weak self] state in
            switch state {
            case .ready:
                print("✅ Connection ready")
                self?.delegate?.networkManager(self!, didChangeConnectionStatus: true)
            case .failed(let error):
                print("❌ Connection failed: \(error)")
                self?.removeConnection(connection)
            case .cancelled:
                print("🔌 Connection cancelled")
                self?.removeConnection(connection)
            default:
                break
            }
        }
    }
    
    private func receiveMessage(on connection: NWConnection) {
        connection.receive(minimumIncompleteLength: 1, maximumLength: 65536) { [weak self] data, _, isComplete, error in
            
            if let error = error {
                print("❌ Receive error: \(error)")
                self?.removeConnection(connection)
                return
            }
            
            if let data = data, !data.isEmpty {
                self?.processReceivedData(data, from: connection)
            }
            
            if !isComplete {
                self?.receiveMessage(on: connection)
            }
        }
    }
    
    private func processReceivedData(_ data: Data, from connection: NWConnection) {
        NSLog("📨 Received data from \(connection.endpoint): \(data.count) bytes")
        let isHandshaked = connectionStates[connection.endpoint.debugDescription] ?? false

        if !isHandshaked {
            // Handle WebSocket handshake
            if let httpRequest = String(data: data, encoding: .utf8) {
                NSLog("📨 HTTP Request: \(httpRequest)")
                if httpRequest.contains("Upgrade: websocket") {
                    NSLog("🤝 WebSocket upgrade request detected")
                    handleWebSocketHandshake(httpRequest, connection: connection)
                    return
                } else {
                    NSLog("❌ Not a WebSocket upgrade request")
                }
            } else {
                NSLog("❌ Could not decode data as UTF-8")
            }
        }

        // Handle WebSocket frames after handshake
        if let message = parseWebSocketFrame(data) {
            if let command = NetworkCommand(from: message) {
                delegate?.networkManager(self, didReceiveCommand: command)
            }
        } else if let command = NetworkCommand(from: data) {
            // Fallback to direct JSON parsing
            delegate?.networkManager(self, didReceiveCommand: command)
        }
    }

    private func handleWebSocketHandshake(_ request: String, connection: NWConnection) {
        NSLog("🤝 Handling WebSocket handshake")
        print("🤝 Handling WebSocket handshake")

        // Extract the WebSocket key
        guard let keyRange = request.range(of: "Sec-WebSocket-Key: "),
              let lineEnd = request.range(of: "\r\n", range: keyRange.upperBound..<request.endIndex) else {
            NSLog("❌ Invalid WebSocket handshake request")
            print("❌ Invalid WebSocket handshake request")
            connection.cancel()
            return
        }

        let key = String(request[keyRange.upperBound..<lineEnd.lowerBound])

        // Generate the accept key
        let acceptKey = generateWebSocketAcceptKey(from: key)

        // Create the handshake response
        let response = """
        HTTP/1.1 101 Switching Protocols\r
        Upgrade: websocket\r
        Connection: Upgrade\r
        Sec-WebSocket-Accept: \(acceptKey)\r
        \r

        """

        // Send the response
        if let responseData = response.data(using: .utf8) {
            connection.send(content: responseData, completion: .contentProcessed { error in
                if let error = error {
                    print("❌ Failed to send handshake response: \(error)")
                    connection.cancel()
                } else {
                    print("✅ WebSocket handshake completed")
                    self.connectionStates[connection.endpoint.debugDescription] = true
                }
            })
        }
    }

    private func generateWebSocketAcceptKey(from key: String) -> String {
        let magicString = "258EAFA5-E914-47DA-95CA-C5AB0DC85B11"
        let combined = key + magicString
        let hash = Insecure.SHA1.hash(data: combined.data(using: .utf8)!)
        return Data(hash).base64EncodedString()
    }

    private func parseWebSocketFrame(_ data: Data) -> Data? {
        // Simple WebSocket frame parsing for text frames
        guard data.count >= 2 else { return nil }

        let firstByte = data[0]
        let secondByte = data[1]

        // Check RSV bits (bits 1-3 of first byte) - they must be 0
        let rsvBits = (firstByte & 0x70) >> 4
        guard rsvBits == 0 else {
            NSLog("❌ Invalid WebSocket frame: RSV bits must be clear (got: \(rsvBits))")
            return nil
        }

        // Check if it's a text frame (opcode 0x1)
        let opcode = firstByte & 0x0F
        guard opcode == 0x1 else { return nil }

        // Check if payload is masked
        let masked = (secondByte & 0x80) != 0
        var payloadLength = Int(secondByte & 0x7F)
        var offset = 2

        // Handle extended payload length
        if payloadLength == 126 {
            guard data.count >= 4 else { return nil }
            payloadLength = Int(data[2]) << 8 | Int(data[3])
            offset = 4
        } else if payloadLength == 127 {
            guard data.count >= 10 else { return nil }
            // For simplicity, we'll limit to smaller payloads
            return nil
        }

        // Handle masking
        if masked {
            guard data.count >= offset + 4 + payloadLength else { return nil }
            let maskKey = Array(data[offset..<offset+4])
            offset += 4

            var payload = Array(data[offset..<offset+payloadLength])
            for i in 0..<payload.count {
                payload[i] ^= maskKey[i % 4]
            }
            return Data(payload)
        } else {
            guard data.count >= offset + payloadLength else { return nil }
            return data.subdata(in: offset..<offset+payloadLength)
        }
    }

    private func createWebSocketFrame(data: Data) -> Data {
        var frame = Data()

        // First byte: FIN=1, RSV1=0, RSV2=0, RSV3=0, Opcode=0x1 (text frame)
        let firstByte: UInt8 = 0x81 // 10000001 in binary
        frame.append(firstByte)

        let payloadLength = data.count

        if payloadLength < 126 {
            // Small payload: length fits in 7 bits
            // Second byte: MASK=0, Payload length
            let secondByte = UInt8(payloadLength)
            frame.append(secondByte)
        } else if payloadLength < 65536 {
            // Medium payload: use 16-bit extended length
            // Second byte: MASK=0, Payload length = 126
            frame.append(126)
            // Next 2 bytes: actual length in network byte order (big-endian)
            frame.append(UInt8((payloadLength >> 8) & 0xFF))
            frame.append(UInt8(payloadLength & 0xFF))
        } else {
            // Large payload: use 64-bit extended length
            // Second byte: MASK=0, Payload length = 127
            frame.append(127)
            // Next 8 bytes: actual length in network byte order (big-endian)
            // For simplicity, we'll use only the lower 32 bits
            frame.append(0) // Upper 32 bits
            frame.append(0)
            frame.append(0)
            frame.append(0)
            frame.append(UInt8((payloadLength >> 24) & 0xFF)) // Lower 32 bits
            frame.append(UInt8((payloadLength >> 16) & 0xFF))
            frame.append(UInt8((payloadLength >> 8) & 0xFF))
            frame.append(UInt8(payloadLength & 0xFF))
        }

        // Append the actual payload data
        frame.append(data)

        return frame
    }
    
    private func removeConnection(_ connection: NWConnection) {
        if let index = connectedClients.firstIndex(where: { $0 === connection }) {
            connectedClients.remove(at: index)
        }

        // Clean up connection state tracking
        connectionStates.removeValue(forKey: connection.endpoint.debugDescription)

        if connectedClients.isEmpty {
            delegate?.networkManager(self, didChangeConnectionStatus: false)
        }
    }
}

// MARK: - NetServiceDelegate
extension NetworkManager: NetServiceDelegate {
    func netServiceDidPublish(_ sender: NetService) {
        isServicePublished = true
        print("✅ Bonjour service published successfully")
    }
    
    func netService(_ sender: NetService, didNotPublish errorDict: [String : NSNumber]) {
        let error = NSError(domain: "BonjourError", code: -1, userInfo: errorDict as [String: Any])
        delegate?.networkManager(self, didFailWithError: error)
        print("❌ Failed to publish Bonjour service: \(errorDict)")
    }
    
    func netServiceDidStop(_ sender: NetService) {
        isServicePublished = false
        print("🛑 Bonjour service stopped")
    }
}
