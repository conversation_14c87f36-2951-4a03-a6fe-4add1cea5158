# InstrumentCapture iOS App - Device 1

✅ **Project Status: FIXED AND WORKING**

The iOS app has been completely fixed and is now building successfully! All previous issues have been resolved:

- ✅ Fixed storyboard compilation errors by switching to programmatic UI
- ✅ Resolved all deprecation warnings with modern iOS APIs
- ✅ Updated project configuration for iOS 17.0+
- ✅ All core functionality is intact and working

This is Device 1 of The Instrument system - a complete iOS app that captures screen content using the iPhone camera and communicates with your MacBook backend via local network.

## Features

- **High-Resolution Camera Capture**: Uses iPhone's advanced camera system (optimized for iPhone 14+)
- **Real-time Preview**: Live camera preview with manual focus and zoom controls
- **Image Batching**: Accumulates multiple captures for comprehensive coverage
- **Network Communication**: Bonjour service discovery and WebSocket communication
- **Image Enhancement**: On-device processing for optimal screen capture quality
- **Local Storage**: Temporary storage with automatic cleanup

## Architecture

### Core Components

1. **CameraManager**: Handles camera operations, capture, and image enhancement
2. **NetworkManager**: Manages Bonjour advertising and WebSocket server
3. **ImageBatchManager**: Handles image batching, compression, and storage
4. **ViewController**: Main UI controller that coordinates all components

### Communication Protocol

The app communicates with the backend using WebSocket messages:

- `capture`: Trigger photo capture (with optional count parameter)
- `send`: Send current batch to backend
- `clear`: Clear current batch
- `status`: Get current app status

## Setup Instructions

### 1. Open in Xcode

1. Open Xcode on your Mac
2. Select "Open a project or file"
3. Navigate to `work/Instrument/ios-app/InstrumentCapture.xcodeproj`
4. Open the project

### 2. Configure Project Settings

1. Select the project in the navigator
2. Under "Signing & Capabilities":
   - Set your Development Team
   - Ensure Bundle Identifier is unique (e.g., `com.yourname.instrumentcapture`)
3. Under "Deployment Info":
   - Set minimum iOS version to 17.0
   - Ensure only iPhone is selected

### 3. Add Required Capabilities

The app requires these capabilities (already configured in Info.plist):
- Camera access
- Local network access
- Bonjour services

### 4. Build and Run

1. Connect your iPhone (iPhone 14+ recommended)
2. Select your device as the build target
3. Click the "Run" button or press Cmd+R
4. Grant camera and network permissions when prompted

## Usage

### Basic Operation

1. **Launch**: App starts camera preview and advertises Bonjour service
2. **Capture**: Tap the camera button or receive capture command from backend
3. **Batch**: Images accumulate in the current batch (max 10 images)
4. **Send**: Tap "Send Batch" or receive send command to transmit images
5. **Clear**: Clear current batch when needed

### Camera Controls

- **Zoom Slider**: Adjust zoom from 0.5x (wide) to 3.0x
- **Tap to Focus**: Tap on preview to focus on specific area
- **Auto Enhancement**: Images are automatically enhanced for screen capture

### Network Integration

The app automatically:
- Advertises as `_instrument._tcp` service on port 8082
- Accepts WebSocket connections from the backend
- Responds to commands and sends acknowledgments

## Integration with Backend

The app integrates seamlessly with the existing backend system:

1. **Discovery**: Backend discovers the app via Bonjour
2. **Connection**: Backend connects via WebSocket
3. **Commands**: Backend sends capture/send commands
4. **Data Flow**: App sends compressed images as base64 strings

## Troubleshooting

### Common Issues

1. **Camera Permission Denied**
   - Go to Settings > Privacy & Security > Camera
   - Enable access for InstrumentCapture

2. **Network Connection Issues**
   - Ensure iPhone and MacBook are on same Wi-Fi
   - Check firewall settings on MacBook
   - Restart the app to re-advertise service

3. **Poor Image Quality**
   - Ensure good lighting on the screen
   - Adjust zoom for optimal distance
   - Clean camera lens

### Debug Information

The app logs detailed information to Xcode console:
- Camera session status
- Network connection events
- Image processing results
- Storage operations

## Performance Optimization

### For Best Results

1. **Device**: Use iPhone 14+ for best camera quality
2. **Distance**: Position 2-3 feet from screen
3. **Lighting**: Avoid glare and reflections
4. **Stability**: Use tripod or gimbal for steady capture
5. **Network**: Use 5GHz Wi-Fi for faster transmission

### Battery Management

- App disables idle timer during use
- Camera session stops when app goes to background
- Automatic cleanup of old stored batches

## Development Notes

### Code Structure

- **Swift 5.0** with iOS 17.0+ deployment target
- **AVFoundation** for camera operations with modern APIs
- **Network** framework for WebSocket server
- **Vision** framework for image enhancement
- **UIKit** for programmatic user interface (no storyboards)

### Key Features

- Thread-safe operations with proper queue management
- Error handling with user-friendly messages
- Memory-efficient image processing
- Robust network communication with reconnection
- Modern iOS APIs with backward compatibility
- Programmatic UI for better reliability

### Future Enhancements

- HDR capture for challenging lighting
- Real-time OCR preview
- Advanced image stabilization
- Cloud backup integration
