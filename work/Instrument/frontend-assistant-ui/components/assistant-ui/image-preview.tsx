import { FC } from "react";

interface ImagePreviewProps {
  images: string[];
  className?: string;
}

export const ImagePreview: FC<ImagePreviewProps> = ({ images, className = "" }) => {
  if (images.length === 0) return null;

  return (
    <div className={`backdrop-blur-md bg-black/70 text-white p-3 rounded-xl shadow-xl ${className}`}>
      <h3 className="m-0 mb-2 text-sm text-slate-300">Captured Images</h3>
      <div className="flex items-center gap-2">
        {images.slice(0, 3).map((image, index) => (
          <img
            key={index}
            src={`data:image/jpeg;base64,${image}`}
            alt={`Capture ${index + 1}`}
            className="w-14 h-14 object-cover rounded-md border border-white/20"
          />
        ))}
        {images.length > 3 && (
          <div className="text-slate-300 text-sm px-2">+{images.length - 3} more</div>
        )}
      </div>
    </div>
  );
};

