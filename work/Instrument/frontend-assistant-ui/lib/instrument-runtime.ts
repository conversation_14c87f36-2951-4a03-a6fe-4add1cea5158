import type {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ThreadHistoryAdapter,
  ThreadMessage
} from "@assistant-ui/react";

export interface DeviceInfo {
  name: string;
  host: string;
  port: number;
}

export interface InstrumentRuntimeConfig {
  backendUrl: string;
  wsUrl: string;
  onDeviceStatusChange?: (connected: boolean, deviceInfo: DeviceInfo | null) => void;
  onImagesReceived?: (images: string[]) => void;
  systemPrompt?: string;
}

export class InstrumentManager {
  private ws: WebSocket | null = null;
  private config: InstrumentRuntimeConfig;
  private deviceConnected = false;
  private deviceInfo: DeviceInfo | null = null;
  private currentImages: string[] = [];
  private messageHistory: Array<{ message: ThreadMessage; parentId: string | null }> = [];

  constructor(config: InstrumentRuntimeConfig) {
    this.config = config;
    this.loadHistoryFromStorage();
    this.connectWebSocket();
  }

  private connectWebSocket() {
    try {
      this.ws = new WebSocket(this.config.wsUrl);

      this.ws.onopen = () => {
        console.log('Connected to backend WebSocket');
      };

      this.ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          this.handleWebSocketMessage(data);
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };

      this.ws.onclose = () => {
        console.log('WebSocket connection closed');
        // Only attempt to reconnect if not manually closed
        if (this.ws) {
          setTimeout(() => this.connectWebSocket(), 3000);
        }
      };

      this.ws.onerror = (error) => {
        console.error('WebSocket error:', error);
        // Don't spam reconnection attempts on error
      };
    } catch (error) {
      console.error('Failed to connect WebSocket:', error);
      // Retry connection after delay on initialization failure
      setTimeout(() => this.connectWebSocket(), 5000);
    }
  }

  private handleWebSocketMessage(data: any) {
    switch (data.type) {
      case 'device_status':
        this.deviceConnected = data.connected;
        this.deviceInfo = data.deviceInfo;
        this.config.onDeviceStatusChange?.(this.deviceConnected, this.deviceInfo);
        break;

      case 'images':
        console.log('🖼️ Images message received from backend:', data);
        if (data.images && Array.isArray(data.images)) {
          console.log(`📥 Received ${data.images.length} images from device`);
          console.log(`   - Image sizes: ${data.images.map((img: string) => img.length)}`);
          this.currentImages = data.images;
          this.config.onImagesReceived?.(data.images);
        } else {
          console.log('⚠️ Images message received but no valid images array');
        }
        break;

      case 'error':
        console.error('Device error:', data.message);
        break;

      default:
        console.log('Unknown message type:', data.type);
    }
  }

  // Send command to device via WebSocket
  public sendDeviceCommand(command: string, params: any = {}) {
    console.log(`📤 Sending device command: ${command}`, params);
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      const message = {
        type: command,
        ...params
      };
      console.log(`   - Full message:`, message);
      this.ws.send(JSON.stringify(message));
    } else {
      console.error('❌ Cannot send command - not connected to backend');
      throw new Error('Not connected to backend');
    }
  }

  // Capture images from device
  public captureImages(count: number = 1) {
    this.sendDeviceCommand('capture', { count });
  }

  // Send command to device
  public sendToDevice() {
    this.sendDeviceCommand('send');
  }

  // Get current device status
  public getDeviceStatus() {
    return {
      connected: this.deviceConnected,
      deviceInfo: this.deviceInfo
    };
  }

  // Get current images
  public getCurrentImages() {
    return this.currentImages;
  }

  // Create the ThreadHistoryAdapter for assistant-ui
  public createHistoryAdapter(): ThreadHistoryAdapter {
    const self = this;
    return {
      async load() {
        // Return the current message history in the correct format
        return {
          messages: self.messageHistory,
          headId: self.messageHistory.length > 0 ? self.messageHistory[self.messageHistory.length - 1].message.id : null
        };
      },

      async append(item) {
        // Add new message to history
        self.messageHistory.push(item);

        // Optional: Persist to localStorage for browser refresh persistence
        try {
          localStorage.setItem('instrument-chat-history', JSON.stringify(self.messageHistory));
        } catch (error) {
          console.warn('Failed to save chat history to localStorage:', error);
        }
      }
    };
  }

  // Load history from localStorage on initialization
  private loadHistoryFromStorage() {
    try {
      const stored = localStorage.getItem('instrument-chat-history');
      if (stored) {
        const parsed = JSON.parse(stored);
        // Handle both old format (ThreadMessage[]) and new format (ExportedMessageRepositoryItem[])
        if (Array.isArray(parsed) && parsed.length > 0) {
          if ('message' in parsed[0] && 'parentId' in parsed[0]) {
            // New format
            this.messageHistory = parsed;
          } else {
            // Old format - convert to new format
            this.messageHistory = parsed.map((msg: ThreadMessage, index: number) => ({
              message: msg,
              parentId: index === 0 ? null : parsed[index - 1].id
            }));
          }
        }
        console.log(`📚 Loaded ${this.messageHistory.length} messages from history`);
      }
    } catch (error) {
      console.warn('Failed to load chat history from localStorage:', error);
      this.messageHistory = [];
    }
  }

  // Create the ChatModelAdapter for assistant-ui
  public createAdapter(): ChatModelAdapter {
    const self = this; // Capture reference to access instance methods and properties
    return {
      async *run({ messages, abortSignal }) {
        try {
          // Extract images from the last user message
          const lastUserMessage = messages.findLast(m => m.role === "user");
          const images: string[] = [];
          let context = "";

          if (lastUserMessage) {
            if (typeof lastUserMessage.content === "string") {
              context = lastUserMessage.content;
            } else if (Array.isArray(lastUserMessage.content)) {
              for (const part of lastUserMessage.content) {
                if (part.type === "text") {
                  context += part.text;
                } else if (part.type === "image") {
                  // Extract base64 from data URL
                  const base64 = part.image.replace(/^data:image\/[a-z]+;base64,/, '');
                  images.push(base64);
                }
              }
            }
          }

          // If no images in message content, use current images
          const imagesToSend = images.length > 0 ? images : (self.currentImages || []);

          // Allow text-only requests or image-only requests
          const hasImages = imagesToSend && imagesToSend.length > 0;
          const hasText = context && context.trim();

          if (!hasImages && !hasText) {
            // Only show error if neither text nor images are provided
            yield {
              content: [
                {
                  type: "text",
                  text: "Please provide either text input or capture some images first using the 'Capture' button."
                }
              ]
            };
            return;
          }

          // Make request to backend
          let response;
          try {
            response = await fetch(`${self.config.backendUrl}/api/chat`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                images: hasImages ? imagesToSend : [],
                context: hasText ? context.trim() : "",
                systemPrompt: self.config.systemPrompt || '',
                messages: messages // Send full conversation history
              }),
              signal: abortSignal
            });
          } catch (fetchError) {
            // Handle network errors (backend not available)
            yield {
              content: [
                {
                  type: "text",
                  text: `Unable to connect to the backend server at ${self.config.backendUrl}. Please ensure the backend is running and accessible.`
                }
              ]
            };
            return;
          }

          if (!response.ok) {
            yield {
              content: [
                {
                  type: "text",
                  text: `Backend server error (${response.status}): ${response.statusText}. Please check the backend logs.`
                }
              ]
            };
            return;
          }

          // Handle streaming response
          const reader = response.body?.getReader();
          const decoder = new TextDecoder();

          if (!reader) {
            throw new Error('No response body');
          }

          let fullText = "";

          while (true) {
            const { done, value } = await reader.read();

            if (done) break;

            const chunk = decoder.decode(value);
            const lines = chunk.split('\n');

            for (const line of lines) {
              if (line.startsWith('data: ')) {
                const data = line.slice(6);

                if (data === '[DONE]') {
                  // Clear images after successful request
                  if (hasImages && imagesToSend === self.currentImages) {
                    self.currentImages = [];
                    self.config.onImagesReceived?.([]);
                  }
                  return;
                }

                try {
                  const parsed = JSON.parse(data);
                  if (parsed.text) {
                    fullText += parsed.text;
                    // Yield the current state
                    yield {
                      content: [{ type: "text", text: fullText }]
                    } as ChatModelRunResult;
                  } else if (parsed.error) {
                    throw new Error(parsed.error);
                  }
                } catch (parseError) {
                  // Ignore parsing errors for partial chunks
                }
              }
            }
          }
        } catch (error) {
          console.error('Analysis error:', error);
          // Provide a user-friendly error message instead of throwing
          yield {
            content: [
              {
                type: "text",
                text: `An error occurred during analysis: ${error instanceof Error ? error.message : 'Unknown error'}. Please try again.`
              }
            ]
          };
        }
      }
    };
  }

  // Clean up WebSocket connection
  public destroy() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }
}

