"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { 
  Clock, 
  AlertTriangle, 
  RefreshCw, 
  Settings,
  Pause,
  Play,
  Square
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface SessionManagerProps {
  onSessionTimeout?: () => void;
  onSessionWarning?: (remainingMinutes: number) => void;
  onReconnect?: () => Promise<void>;
  isConnected: boolean;
  className?: string;
}

interface SessionState {
  startTime: number | null;
  remainingTime: number;
  isNearTimeout: boolean;
  isPaused: boolean;
  totalDuration: number;
}

const SESSION_DURATION = 30 * 60 * 1000; // 30 minutes in milliseconds
const WARNING_THRESHOLD = 5 * 60 * 1000; // 5 minutes warning

export function SessionManager({ 
  onSessionTimeout, 
  onSessionWarning, 
  onReconnect,
  isConnected,
  className 
}: SessionManagerProps) {
  const [sessionState, setSessionState] = useState<SessionState>({
    startTime: null,
    remainingTime: SESSION_DURATION,
    isNearTimeout: false,
    isPaused: false,
    totalDuration: SESSION_DURATION
  });

  const [isReconnecting, setIsReconnecting] = useState(false);

  // Start session timer
  const startSession = useCallback(() => {
    const now = Date.now();
    setSessionState(prev => ({
      ...prev,
      startTime: now,
      remainingTime: SESSION_DURATION,
      isNearTimeout: false,
      isPaused: false
    }));
  }, []);

  // Pause/resume session
  const togglePause = useCallback(() => {
    setSessionState(prev => ({
      ...prev,
      isPaused: !prev.isPaused
    }));
  }, []);

  // Reset session
  const resetSession = useCallback(() => {
    setSessionState({
      startTime: null,
      remainingTime: SESSION_DURATION,
      isNearTimeout: false,
      isPaused: false,
      totalDuration: SESSION_DURATION
    });
  }, []);

  // Handle reconnection
  const handleReconnect = useCallback(async () => {
    if (!onReconnect) return;
    
    try {
      setIsReconnecting(true);
      await onReconnect();
      // Reset session on successful reconnect
      resetSession();
      startSession();
    } catch (error) {
      console.error('Reconnection failed:', error);
    } finally {
      setIsReconnecting(false);
    }
  }, [onReconnect, resetSession, startSession]);

  // Update timer every second
  useEffect(() => {
    if (!sessionState.startTime || sessionState.isPaused) return;

    const interval = setInterval(() => {
      const now = Date.now();
      const elapsed = now - sessionState.startTime!;
      const remaining = Math.max(0, SESSION_DURATION - elapsed);

      setSessionState(prev => ({
        ...prev,
        remainingTime: remaining,
        isNearTimeout: remaining <= WARNING_THRESHOLD && remaining > 0
      }));

      // Trigger warning
      if (remaining <= WARNING_THRESHOLD && remaining > 0 && !sessionState.isNearTimeout) {
        const remainingMinutes = Math.ceil(remaining / (60 * 1000));
        onSessionWarning?.(remainingMinutes);
      }

      // Trigger timeout
      if (remaining === 0) {
        onSessionTimeout?.();
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [sessionState.startTime, sessionState.isPaused, sessionState.isNearTimeout, onSessionTimeout, onSessionWarning]);

  // Auto-start session when connected
  useEffect(() => {
    if (isConnected && !sessionState.startTime) {
      startSession();
    }
  }, [isConnected, sessionState.startTime, startSession]);

  // Format time display
  const formatTime = (milliseconds: number): string => {
    const totalSeconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  // Calculate progress percentage
  const progressPercentage = ((SESSION_DURATION - sessionState.remainingTime) / SESSION_DURATION) * 100;

  return (
    <div className={cn("bg-white border border-gray-200 rounded-lg p-4 shadow-sm", className)}>
      {/* Header */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          <Clock className="w-4 h-4 text-gray-500" />
          <span className="text-sm font-medium text-gray-700">Session Timer</span>
        </div>
        
        {sessionState.isNearTimeout && (
          <div className="flex items-center gap-1 text-amber-600">
            <AlertTriangle className="w-4 h-4" />
            <span className="text-xs font-medium">Warning</span>
          </div>
        )}
      </div>

      {/* Time display */}
      <div className="mb-3">
        <div className={cn(
          "text-2xl font-mono font-bold",
          sessionState.isNearTimeout ? "text-amber-600" : "text-gray-900"
        )}>
          {formatTime(sessionState.remainingTime)}
        </div>
        <div className="text-xs text-gray-500">
          {sessionState.isPaused ? "Paused" : "Remaining"}
        </div>
      </div>

      {/* Progress bar */}
      <div className="mb-4">
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className={cn(
              "h-2 rounded-full transition-all duration-1000",
              sessionState.isNearTimeout ? "bg-amber-500" : "bg-blue-500"
            )}
            style={{ width: `${progressPercentage}%` }}
          />
        </div>
      </div>

      {/* Controls */}
      <div className="space-y-2">
        {/* Session controls */}
        <div className="flex gap-2">
          <Button
            onClick={togglePause}
            variant="outline"
            size="sm"
            disabled={!sessionState.startTime}
            className="flex-1"
          >
            {sessionState.isPaused ? (
              <>
                <Play className="w-3 h-3 mr-1" />
                Resume
              </>
            ) : (
              <>
                <Pause className="w-3 h-3 mr-1" />
                Pause
              </>
            )}
          </Button>

          <Button
            onClick={resetSession}
            variant="outline"
            size="sm"
            className="flex-1"
          >
            <Square className="w-3 h-3 mr-1" />
            Reset
          </Button>
        </div>

        {/* Reconnect button */}
        {!isConnected && (
          <Button
            onClick={handleReconnect}
            disabled={isReconnecting}
            variant="default"
            size="sm"
            className="w-full"
          >
            {isReconnecting ? (
              <>
                <RefreshCw className="w-3 h-3 mr-1 animate-spin" />
                Reconnecting...
              </>
            ) : (
              <>
                <RefreshCw className="w-3 h-3 mr-1" />
                Reconnect Session
              </>
            )}
          </Button>
        )}

        {/* Warning message */}
        {sessionState.isNearTimeout && (
          <div className="text-xs text-amber-600 bg-amber-50 p-2 rounded border border-amber-200">
            ⚠️ Session will timeout soon. The system will auto-reconnect to preserve your conversation history.
          </div>
        )}

        {/* Session info */}
        <div className="text-xs text-gray-500 space-y-1">
          <div>• Sessions are limited to 30 minutes</div>
          <div>• Auto-reconnect preserves conversation</div>
          <div>• Use pause to take breaks</div>
        </div>
      </div>
    </div>
  );
}

// Hook for session management
export function useSessionManager(onTimeout?: () => void, onWarning?: (minutes: number) => void) {
  const [sessionActive, setSessionActive] = useState(false);
  const [sessionWarning, setSessionWarning] = useState(false);

  const handleTimeout = useCallback(() => {
    setSessionActive(false);
    onTimeout?.();
  }, [onTimeout]);

  const handleWarning = useCallback((minutes: number) => {
    setSessionWarning(true);
    onWarning?.(minutes);
  }, [onWarning]);

  const startSession = useCallback(() => {
    setSessionActive(true);
    setSessionWarning(false);
  }, []);

  const endSession = useCallback(() => {
    setSessionActive(false);
    setSessionWarning(false);
  }, []);

  return {
    sessionActive,
    sessionWarning,
    startSession,
    endSession,
    handleTimeout,
    handleWarning
  };
}
