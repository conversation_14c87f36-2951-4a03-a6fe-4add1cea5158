"use client";

import React, { useEffect, useCallback, useState } from 'react';
import { Button } from '@/components/ui/button';
import { 
  Keyboard, 
  Zap, 
  Mic, 
  Volume2, 
  Square,
  Info
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface KeyboardControlsProps {
  onTriggerResponse?: () => void;
  onToggleRecording?: () => void;
  onToggleMute?: () => void;
  onClearBuffer?: () => void;
  isRecording?: boolean;
  isMuted?: boolean;
  isConnected?: boolean;
  className?: string;
}

interface KeyboardShortcut {
  key: string;
  description: string;
  action: () => void;
  icon: React.ReactNode;
  enabled: boolean;
}

export function KeyboardControls({
  onTriggerResponse,
  onToggleRecording,
  onToggleMute,
  onClearBuffer,
  isRecording = false,
  isMuted = false,
  isConnected = false,
  className
}: KeyboardControlsProps) {
  const [activeKeys, setActiveKeys] = useState<Set<string>>(new Set());
  const [showHelp, setShowHelp] = useState(false);

  // Define keyboard shortcuts
  const shortcuts: KeyboardShortcut[] = [
    {
      key: 'Space',
      description: 'Trigger AI Response',
      action: () => onTriggerResponse?.(),
      icon: <Zap className="w-4 h-4" />,
      enabled: isConnected
    },
    {
      key: 'KeyR',
      description: 'Toggle Recording',
      action: () => onToggleRecording?.(),
      icon: <Mic className="w-4 h-4" />,
      enabled: isConnected
    },
    {
      key: 'KeyM',
      description: 'Toggle Mute',
      action: () => onToggleMute?.(),
      icon: <Volume2 className="w-4 h-4" />,
      enabled: isConnected
    },
    {
      key: 'KeyC',
      description: 'Clear Audio Buffer',
      action: () => onClearBuffer?.(),
      icon: <Square className="w-4 h-4" />,
      enabled: isConnected
    }
  ];

  // Handle keyboard events
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    // Prevent shortcuts when typing in input fields
    if (event.target instanceof HTMLInputElement || 
        event.target instanceof HTMLTextAreaElement ||
        event.target instanceof HTMLSelectElement) {
      return;
    }

    // Prevent default for space to avoid page scroll
    if (event.code === 'Space') {
      event.preventDefault();
    }

    // Don't trigger on repeat
    if (event.repeat) return;

    // Add to active keys for visual feedback
    setActiveKeys(prev => new Set(prev).add(event.code));

    // Find and execute shortcut
    const shortcut = shortcuts.find(s => s.key === event.code && s.enabled);
    if (shortcut) {
      event.preventDefault();
      shortcut.action();
    }

    // Toggle help with '?'
    if (event.key === '?' && !event.shiftKey) {
      setShowHelp(prev => !prev);
    }
  }, [shortcuts]);

  const handleKeyUp = useCallback((event: KeyboardEvent) => {
    setActiveKeys(prev => {
      const newSet = new Set(prev);
      newSet.delete(event.code);
      return newSet;
    });
  }, []);

  // Set up keyboard event listeners
  useEffect(() => {
    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('keyup', handleKeyUp);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('keyup', handleKeyUp);
    };
  }, [handleKeyDown, handleKeyUp]);

  // Auto-hide help after 10 seconds
  useEffect(() => {
    if (showHelp) {
      const timer = setTimeout(() => setShowHelp(false), 10000);
      return () => clearTimeout(timer);
    }
  }, [showHelp]);

  return (
    <div className={cn("space-y-4", className)}>
      {/* Help toggle button */}
      <Button
        onClick={() => setShowHelp(!showHelp)}
        variant="outline"
        size="sm"
        className="w-full"
      >
        <Keyboard className="w-4 h-4 mr-2" />
        {showHelp ? 'Hide' : 'Show'} Keyboard Shortcuts
      </Button>

      {/* Keyboard shortcuts help */}
      {showHelp && (
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-3">
            <Info className="w-4 h-4 text-blue-500" />
            <span className="text-sm font-medium text-gray-700">Keyboard Shortcuts</span>
          </div>
          
          <div className="space-y-2">
            {shortcuts.map((shortcut) => (
              <div 
                key={shortcut.key}
                className={cn(
                  "flex items-center justify-between p-2 rounded",
                  shortcut.enabled ? "bg-white" : "bg-gray-100 opacity-50",
                  activeKeys.has(shortcut.key) && shortcut.enabled ? "ring-2 ring-blue-500" : ""
                )}
              >
                <div className="flex items-center gap-2">
                  {shortcut.icon}
                  <span className="text-sm text-gray-700">{shortcut.description}</span>
                </div>
                
                <kbd className={cn(
                  "px-2 py-1 text-xs font-mono rounded border",
                  shortcut.enabled ? "bg-gray-100 border-gray-300" : "bg-gray-200 border-gray-400",
                  activeKeys.has(shortcut.key) && shortcut.enabled ? "bg-blue-100 border-blue-300" : ""
                )}>
                  {shortcut.key === 'Space' ? '␣' : 
                   shortcut.key.startsWith('Key') ? shortcut.key.slice(3) : 
                   shortcut.key}
                </kbd>
              </div>
            ))}
          </div>

          <div className="mt-3 pt-3 border-t border-gray-200">
            <div className="text-xs text-gray-500 space-y-1">
              <div>• Press <kbd className="px-1 py-0.5 bg-gray-200 rounded text-xs">?</kbd> to toggle this help</div>
              <div>• Shortcuts work when not typing in input fields</div>
              <div>• Visual feedback shows active keys</div>
            </div>
          </div>
        </div>
      )}

      {/* Quick action buttons (backup to keyboard) */}
      <div className="grid grid-cols-2 gap-2">
        <Button
          onClick={onTriggerResponse}
          disabled={!isConnected}
          variant="default"
          size="sm"
          className={cn(
            "transition-all",
            activeKeys.has('Space') && isConnected ? "ring-2 ring-blue-500 scale-95" : ""
          )}
        >
          <Zap className="w-3 h-3 mr-1" />
          Trigger
        </Button>

        <Button
          onClick={onToggleRecording}
          disabled={!isConnected}
          variant={isRecording ? "destructive" : "secondary"}
          size="sm"
          className={cn(
            "transition-all",
            activeKeys.has('KeyR') && isConnected ? "ring-2 ring-blue-500 scale-95" : ""
          )}
        >
          <Mic className="w-3 h-3 mr-1" />
          {isRecording ? 'Stop' : 'Record'}
        </Button>

        <Button
          onClick={onToggleMute}
          disabled={!isConnected}
          variant={isMuted ? "destructive" : "secondary"}
          size="sm"
          className={cn(
            "transition-all",
            activeKeys.has('KeyM') && isConnected ? "ring-2 ring-blue-500 scale-95" : ""
          )}
        >
          <Volume2 className="w-3 h-3 mr-1" />
          {isMuted ? 'Unmute' : 'Mute'}
        </Button>

        <Button
          onClick={onClearBuffer}
          disabled={!isConnected}
          variant="outline"
          size="sm"
          className={cn(
            "transition-all",
            activeKeys.has('KeyC') && isConnected ? "ring-2 ring-blue-500 scale-95" : ""
          )}
        >
          <Square className="w-3 h-3 mr-1" />
          Clear
        </Button>
      </div>

      {/* Status indicators */}
      <div className="flex justify-between text-xs text-gray-500">
        <div className="flex items-center gap-1">
          <div className={cn(
            "w-2 h-2 rounded-full",
            isRecording ? "bg-red-500 animate-pulse" : "bg-gray-300"
          )} />
          {isRecording ? "Recording" : "Stopped"}
        </div>
        
        <div className="flex items-center gap-1">
          <div className={cn(
            "w-2 h-2 rounded-full",
            isMuted ? "bg-red-500" : "bg-green-500"
          )} />
          {isMuted ? "Muted" : "Audio On"}
        </div>
      </div>
    </div>
  );
}

// Hook for keyboard shortcut management
export function useKeyboardShortcuts(shortcuts: Record<string, () => void>) {
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Skip if typing in input fields
      if (event.target instanceof HTMLInputElement || 
          event.target instanceof HTMLTextAreaElement ||
          event.target instanceof HTMLSelectElement) {
        return;
      }

      const handler = shortcuts[event.code];
      if (handler && !event.repeat) {
        event.preventDefault();
        handler();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [shortcuts]);
}

// Visual feedback component for active shortcuts
export function KeyboardFeedback({ activeKey, description }: { activeKey: string | null, description?: string }) {
  if (!activeKey) return null;

  return (
    <div className="fixed top-4 left-1/2 transform -translate-x-1/2 z-50">
      <div className="bg-black/80 text-white px-4 py-2 rounded-lg backdrop-blur-sm">
        <div className="flex items-center gap-2">
          <kbd className="px-2 py-1 bg-white/20 rounded text-sm font-mono">
            {activeKey === 'Space' ? '␣' : activeKey.replace('Key', '')}
          </kbd>
          {description && <span className="text-sm">{description}</span>}
        </div>
      </div>
    </div>
  );
}
