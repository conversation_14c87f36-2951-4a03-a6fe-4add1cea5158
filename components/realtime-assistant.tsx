"use client";

import React, { useEffect, useState, useCallback, useRef } from 'react';
import { 
  AssistantRuntimeProvider, 
  ThreadPrimitive, 
  ComposerPrimitive,
  MessagePrimitive,
  useAssistantInstructions 
} from '@assistant-ui/react';
import { Button } from '@/components/ui/button';
import { 
  <PERSON><PERSON>, 
  MicOff, 
  Volume2, 
  VolumeX, 
  Play, 
  Pause, 
  Square,
  Settings,
  Zap
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { RealtimeWebSocketManager, RealtimeAssistantRuntime } from '@/lib/realtime-runtime';
import { AudioInputManager } from '@/lib/audio-input';
import { AudioOutputManager } from '@/lib/audio-output';

interface RealtimeAssistantProps {
  ephemeralKey: string;
  systemPrompt?: string;
}

interface AudioControls {
  isRecording: boolean;
  isMuted: boolean;
  playbackRate: number;
  volume: number;
  isConnected: boolean;
}

interface TranscriptEntry {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: number;
}

const INTERVIEW_SYSTEM_PROMPT = `You are an expert system design interviewer for senior technical roles. 

Your role:
- Listen to conversations between the interviewee and human interviewer
- Only respond when explicitly triggered (when someone says "AI, your turn" or similar)
- Provide insightful questions, clarifications, or feedback about system design concepts
- Help guide the interview toward deeper technical discussions
- Maintain context across the entire conversation

Key behaviors:
- Wait for manual triggers - do not interrupt natural conversation flow
- Ask probing questions about scalability, reliability, and trade-offs
- Suggest alternative approaches or edge cases to consider
- Keep responses concise but technically deep
- Adapt difficulty based on the candidate's responses

Remember: This is a practice session to help build genuine interview skills.`;

export function RealtimeAssistant({ ephemeralKey, systemPrompt }: RealtimeAssistantProps) {
  // Audio managers
  const [audioInput, setAudioInput] = useState<AudioInputManager | null>(null);
  const [audioOutput, setAudioOutput] = useState<AudioOutputManager | null>(null);
  const [runtime, setRuntime] = useState<RealtimeAssistantRuntime | null>(null);
  
  // UI state
  const [audioControls, setAudioControls] = useState<AudioControls>({
    isRecording: false,
    isMuted: false,
    playbackRate: 1.0,
    volume: 1.0,
    isConnected: false
  });
  
  const [transcripts, setTranscripts] = useState<TranscriptEntry[]>([]);
  const [connectionStatus, setConnectionStatus] = useState<'disconnected' | 'connecting' | 'connected'>('disconnected');
  const [error, setError] = useState<string | null>(null);
  
  // Refs for keyboard handling
  const triggerKeyRef = useRef<boolean>(false);

  // Initialize audio systems
  useEffect(() => {
    const initializeAudio = async () => {
      try {
        setConnectionStatus('connecting');
        
        // Initialize audio output
        const outputManager = new AudioOutputManager({
          onError: (err) => setError(err.message),
          onPlaybackStart: () => console.log('Playback started'),
          onPlaybackEnd: () => console.log('Playback ended')
        });
        await outputManager.initialize();
        setAudioOutput(outputManager);

        // Initialize audio input
        const inputManager = new AudioInputManager({
          onError: (err) => setError(err.message)
        });
        await inputManager.initialize();
        setAudioInput(inputManager);

        // Initialize realtime runtime
        const realtimeRuntime = new RealtimeAssistantRuntime(
          ephemeralKey, 
          systemPrompt || INTERVIEW_SYSTEM_PROMPT
        );
        
        // Set up audio data flow
        inputManager.config.onAudioData = (audioData) => {
          // Audio data will be sent to WebSocket via runtime
        };

        await realtimeRuntime.initialize();
        setRuntime(realtimeRuntime);
        
        // Start recording
        inputManager.startRecording();
        
        setAudioControls(prev => ({ 
          ...prev, 
          isRecording: true, 
          isConnected: true 
        }));
        setConnectionStatus('connected');
        
      } catch (error) {
        console.error('Failed to initialize audio:', error);
        setError(`Initialization failed: ${error}`);
        setConnectionStatus('disconnected');
      }
    };

    initializeAudio();

    return () => {
      audioInput?.destroy();
      audioOutput?.destroy();
      runtime?.destroy();
    };
  }, [ephemeralKey, systemPrompt]);

  // Keyboard event handling for manual trigger
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.code === 'Space' && !event.repeat && !triggerKeyRef.current) {
        event.preventDefault();
        triggerKeyRef.current = true;
        handleManualTrigger();
      }
    };

    const handleKeyUp = (event: KeyboardEvent) => {
      if (event.code === 'Space') {
        triggerKeyRef.current = false;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('keyup', handleKeyUp);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('keyup', handleKeyUp);
    };
  }, [runtime]);

  // Manual trigger function (spacebar or button)
  const handleManualTrigger = useCallback(() => {
    if (!runtime) return;
    
    console.log('Manual trigger activated');
    runtime.triggerResponse();
    
    // Add visual feedback
    setTranscripts(prev => [...prev, {
      id: `trigger-${Date.now()}`,
      text: '[AI Response Triggered]',
      isUser: false,
      timestamp: Date.now()
    }]);
  }, [runtime]);

  // Audio control handlers
  const toggleRecording = useCallback(() => {
    if (!audioInput) return;
    
    if (audioControls.isRecording) {
      audioInput.stopRecording();
    } else {
      audioInput.startRecording();
    }
    
    setAudioControls(prev => ({ 
      ...prev, 
      isRecording: !prev.isRecording 
    }));
  }, [audioInput, audioControls.isRecording]);

  const toggleMute = useCallback(() => {
    if (!audioOutput) return;
    
    const newMutedState = audioOutput.toggleMute();
    setAudioControls(prev => ({ 
      ...prev, 
      isMuted: newMutedState 
    }));
  }, [audioOutput]);

  const handlePlaybackRateChange = useCallback((rate: number) => {
    if (!audioOutput) return;
    
    audioOutput.setPlaybackRate(rate);
    setAudioControls(prev => ({ 
      ...prev, 
      playbackRate: rate 
    }));
  }, [audioOutput]);

  const handleVolumeChange = useCallback((volume: number) => {
    if (!audioOutput) return;
    
    audioOutput.setVolume(volume);
    setAudioControls(prev => ({ 
      ...prev, 
      volume 
    }));
  }, [audioOutput]);

  // System instructions provider
  const SystemInstructionsProvider = ({ children }: { children: React.ReactNode }) => {
    useAssistantInstructions(systemPrompt || INTERVIEW_SYSTEM_PROMPT);
    return <>{children}</>;
  };

  if (connectionStatus === 'connecting') {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p>Connecting to OpenAI Realtime API...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center max-w-md">
          <div className="text-red-500 mb-4">⚠️ Connection Error</div>
          <p className="text-gray-600 mb-4">{error}</p>
          <Button onClick={() => window.location.reload()}>
            Retry Connection
          </Button>
        </div>
      </div>
    );
  }

  if (!runtime) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <p>Initializing...</p>
      </div>
    );
  }

  return (
    <AssistantRuntimeProvider runtime={runtime as any}>
      <div className="flex flex-col h-screen bg-gray-50">
        {/* Header with status */}
        <header className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-xl font-semibold text-gray-900">
                Speech-to-Speech Interview Assistant
              </h1>
              <p className="text-sm text-gray-500">
                Press <kbd className="px-2 py-1 bg-gray-100 rounded text-xs">Space</kbd> to trigger AI response
              </p>
            </div>
            
            <div className="flex items-center gap-4">
              {/* Connection status */}
              <div className={cn(
                "status-indicator",
                audioControls.isConnected ? "connected" : "disconnected"
              )}>
                <div className={cn(
                  "w-2 h-2 rounded-full",
                  audioControls.isConnected ? "bg-green-500" : "bg-red-500"
                )} />
                {audioControls.isConnected ? "Connected" : "Disconnected"}
              </div>
            </div>
          </div>
        </header>

        {/* Main content area */}
        <div className="flex-1 flex">
          {/* Chat area */}
          <div className="flex-1 flex flex-col">
            <SystemInstructionsProvider>
              <ThreadPrimitive.Root className="flex-1 flex flex-col">
                <ThreadPrimitive.Viewport className="flex-1 overflow-y-auto p-6">
                  <ThreadPrimitive.Empty>
                    <div className="text-center py-12">
                      <div className="text-gray-400 mb-4">
                        <Mic className="w-12 h-12 mx-auto" />
                      </div>
                      <h3 className="text-lg font-medium text-gray-900 mb-2">
                        Ready for Interview Practice
                      </h3>
                      <p className="text-gray-500 max-w-md mx-auto">
                        Start your mock system design interview. The AI will listen and provide 
                        feedback when you trigger it with the spacebar or button.
                      </p>
                    </div>
                  </ThreadPrimitive.Empty>
                  
                  <ThreadPrimitive.Messages />
                </ThreadPrimitive.Viewport>
              </ThreadPrimitive.Root>
            </SystemInstructionsProvider>
          </div>
        </div>

        {/* Audio controls overlay */}
        <div className="audio-controls">
          {/* Manual trigger button */}
          <Button
            onClick={handleManualTrigger}
            className="audio-control-button primary mb-2 w-full"
            disabled={!audioControls.isConnected}
          >
            <Zap className="w-4 h-4 mr-2" />
            Trigger AI Response
          </Button>

          {/* Recording control */}
          <Button
            onClick={toggleRecording}
            variant={audioControls.isRecording ? "destructive" : "secondary"}
            className="audio-control-button mb-2 w-full"
          >
            {audioControls.isRecording ? (
              <>
                <Square className="w-4 h-4 mr-2" />
                Stop Recording
              </>
            ) : (
              <>
                <Mic className="w-4 h-4 mr-2" />
                Start Recording
              </>
            )}
          </Button>

          {/* Mute control */}
          <Button
            onClick={toggleMute}
            variant="secondary"
            className="audio-control-button mb-2 w-full"
          >
            {audioControls.isMuted ? (
              <>
                <VolumeX className="w-4 h-4 mr-2" />
                Unmute
              </>
            ) : (
              <>
                <Volume2 className="w-4 h-4 mr-2" />
                Mute
              </>
            )}
          </Button>

          {/* Playback rate control */}
          <div className="mb-2">
            <label className="block text-xs text-gray-600 mb-1">
              Speed: {audioControls.playbackRate}x
            </label>
            <input
              type="range"
              min="0.5"
              max="2"
              step="0.1"
              value={audioControls.playbackRate}
              onChange={(e) => handlePlaybackRateChange(parseFloat(e.target.value))}
              className="w-full"
            />
          </div>

          {/* Volume control */}
          <div>
            <label className="block text-xs text-gray-600 mb-1">
              Volume: {Math.round(audioControls.volume * 100)}%
            </label>
            <input
              type="range"
              min="0"
              max="1"
              step="0.1"
              value={audioControls.volume}
              onChange={(e) => handleVolumeChange(parseFloat(e.target.value))}
              className="w-full"
            />
          </div>
        </div>

        {/* Live transcript overlay */}
        {transcripts.length > 0 && (
          <div className="transcript-container">
            <div className="transcript-label mb-2">Live Transcript</div>
            <div className="max-h-32 overflow-y-auto">
              {transcripts.slice(-3).map((entry) => (
                <div key={entry.id} className="mb-1">
                  <span className={cn(
                    "text-xs font-medium",
                    entry.isUser ? "text-blue-600" : "text-green-600"
                  )}>
                    {entry.isUser ? "You" : "AI"}:
                  </span>
                  <span className="transcript-text ml-2">{entry.text}</span>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </AssistantRuntimeProvider>
  );
}
